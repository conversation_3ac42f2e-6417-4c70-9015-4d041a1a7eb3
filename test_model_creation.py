#!/usr/bin/env python3
import sys
from pathlib import Path

# Add current directory to Python path
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

import timm
import torch

print("=== Testing model creation step by step ===")

print("\n1. Direct timm model creation (no pretrained):")
try:
    model1 = timm.create_model('vit_small_patch16_224', pretrained=False, num_classes=2)
    print(f"   ✓ Success: embed_dim={model1.embed_dim}, num_classes={model1.head.out_features}")
except Exception as e:
    print(f"   ✗ Error: {e}")

print("\n2. Using our build_vit_model function:")
try:
    from training.models.build_vit import build_vit_model
    model2 = build_vit_model(num_classes=2, image_size=160, pretrained=False)
    print(f"   ✓ Success: embed_dim={model2.embed_dim}, num_classes={model2.head.out_features}")
except Exception as e:
    print(f"   ✗ Error: {e}")

print("\n3. Testing forward pass:")
try:
    x = torch.randn(1, 3, 160, 160)
    with torch.no_grad():
        output = model2(x)
    print(f"   ✓ Forward pass success: input {x.shape} -> output {output.shape}")
except Exception as e:
    print(f"   ✗ Forward pass error: {e}")

print("\n4. Checking if there are any cached models:")
import os
cache_dirs = [
    Path.home() / '.cache' / 'torch' / 'hub',
    Path.home() / '.cache' / 'huggingface',
    Path.home() / '.cache' / 'timm',
]

for cache_dir in cache_dirs:
    if cache_dir.exists():
        files = list(cache_dir.rglob('*vit*'))
        if files:
            print(f"   Found cached files in {cache_dir}:")
            for f in files[:5]:  # Show first 5
                print(f"     {f}")
        else:
            print(f"   No ViT files in {cache_dir}")
    else:
        print(f"   Cache dir doesn't exist: {cache_dir}")
