import torch
import torch.nn as nn
import timm


def build_vit_model(num_classes: int = 2, image_size: int = 160, pretrained: bool = True, model_name: str = 'vit_small_patch16_224'):
    # Use specified ViT model and allow positional embedding interpolation to different sizes
    model = timm.create_model(model_name, pretrained=pretrained, num_classes=num_classes)
    # timm will handle pos_embed interpolation on first forward with different size
    return model


def build_vit_from_checkpoint(checkpoint_path: str, num_classes: int = 2, image_size: int = 160):
    """Build ViT model with configuration auto-detected from checkpoint."""
    ckpt = torch.load(checkpoint_path, map_location='cpu')
    state_dict = ckpt['model_ema'] if 'model_ema' in ckpt else ckpt

    # Check embed_dim from patch_embed.proj.weight shape
    embed_dim = state_dict['patch_embed.proj.weight'].shape[0]
    print(f"Detected embed_dim from checkpoint: {embed_dim}")

    # Determine model type based on embed_dim
    if embed_dim == 384:
        model_name = 'vit_small_patch16_224'
    elif embed_dim == 768:
        model_name = 'vit_base_patch16_224'
    elif embed_dim == 192:
        model_name = 'vit_tiny_patch16_224'
    else:
        raise ValueError(f"Unsupported embed_dim: {embed_dim}. Supported: 192 (tiny), 384 (small), 768 (base)")

    print(f"Using model: {model_name}")

    # Create model with detected configuration
    model = timm.create_model(model_name, pretrained=False, num_classes=num_classes)
    model.load_state_dict(state_dict)

    return model

