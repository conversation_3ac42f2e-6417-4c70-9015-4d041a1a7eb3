import argparse
import sys
from pathlib import Path

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader

# Add the project root to Python path
sys.path.append(str(Path(__file__).parent.parent))

from training.datasets.corn_patches import CornLabeledDataset, CornUnlabeledDataset
from training.datasets.transforms_fixmatch import build_transforms
from training.models.build_vit import build_vit_model
from training.utils.ema import ModelEMA
from training.utils.logger import TBLogger
from training.utils.metrics import binary_metrics_from_logits


def parse_args():
    ap = argparse.ArgumentParser()
    ap.add_argument('--splits_dir', type=Path, default=Path('training')/ 'data' / 'splits')
    ap.add_argument('--images_root', type=Path, default=Path('.'))
    ap.add_argument('--out_dir', type=Path, default=Path('runs')/ 'fixmatch_vit')
    ap.add_argument('--epochs', type=int, default=150)
    ap.add_argument('--image_size', type=int, default=160)
    ap.add_argument('--lr', type=float, default=3e-4)
    ap.add_argument('--weight_decay', type=float, default=0.05)
    ap.add_argument('--labeled_bs', type=int, default=64)
    ap.add_argument('--unlabeled_bs', type=int, default=192)
    ap.add_argument('--tau', type=float, default=0.95)
    ap.add_argument('--lambda_u', type=float, default=1.0)
    ap.add_argument('--rampup_epochs', type=int, default=10)
    ap.add_argument('--ema_decay', type=float, default=0.999)
    ap.add_argument('--use_class_weight', action='store_true')
    ap.add_argument('--num_workers', type=int, default=8)
    ap.add_argument('--device', type=str, default='cuda', choices=['cpu','cuda','directml'])
    ap.add_argument('--pretrained', action='store_true', help='Use ImageNet pretrained weights')
    return ap.parse_args()


def linear_rampup(current, rampup_length):
    if rampup_length == 0:
        return 1.0
    else:
        current = max(0.0, min(1.0, current / rampup_length))
        return float(current)


def main():
    args = parse_args()
    # Device selection: cpu / cuda / directml
    if args.device == 'directml':
        import torch_directml
        device = torch_directml.device()
        amp_enabled = False  # AMP not supported on DirectML
    elif args.device == 'cuda' and torch.cuda.is_available():
        device = torch.device('cuda')
        amp_enabled = True
    else:
        device = torch.device('cpu')
        amp_enabled = False
    args.out_dir.mkdir(parents=True, exist_ok=True)

    # Transforms
    weak_tfm, strong_tfm, eval_tfm = build_transforms(args.image_size)

    # Datasets
    train_csv = args.splits_dir / 'labeled_train.csv'
    val_csv = args.splits_dir / 'labeled_val.csv'
    unl_csv = args.splits_dir / 'unlabeled.csv'

    ds_train = CornLabeledDataset(train_csv, image_root=args.images_root, transform=weak_tfm)
    ds_val = CornLabeledDataset(val_csv, image_root=args.images_root, transform=eval_tfm)
    ds_unl = CornUnlabeledDataset(unl_csv, image_root=args.images_root, weak_transform=weak_tfm, strong_transform=strong_tfm)

    # Class weights (for imbalance)
    class_weights = None
    if args.use_class_weight:
        # Estimate class weights from train set labels
        import numpy as np
        ys = [y for _, y in ds_train.items]
        n0 = sum(1 for v in ys if v == 0)
        n1 = sum(1 for v in ys if v == 1)
        w0 = len(ys) / (2.0 * n0 + 1e-6)
        w1 = len(ys) / (2.0 * n1 + 1e-6)
        class_weights = torch.tensor([w0, w1], dtype=torch.float32, device=device)

    # Loaders
    dl_train = DataLoader(ds_train, batch_size=args.labeled_bs, shuffle=True, num_workers=args.num_workers, pin_memory=True)
    dl_unl = DataLoader(ds_unl, batch_size=args.unlabeled_bs, shuffle=True, num_workers=args.num_workers, pin_memory=True, drop_last=True)
    dl_val = DataLoader(ds_val, batch_size=256, shuffle=False, num_workers=args.num_workers, pin_memory=True)

    # Model & EMA
    print(f"Creating model with pretrained={args.pretrained}")
    model = build_vit_model(num_classes=2, image_size=args.image_size, pretrained=args.pretrained).to(device)
    ema = ModelEMA(model, decay=args.ema_decay)

    # Optimizer & loss
    opt = optim.AdamW(model.parameters(), lr=args.lr, weight_decay=args.weight_decay)
    ce = nn.CrossEntropyLoss(weight=class_weights)

    scaler = torch.cuda.amp.GradScaler(enabled=amp_enabled)

    logger = TBLogger(args.out_dir)

    global_step = 0
    best_val_auc = -1.0

    for epoch in range(1, args.epochs + 1):
        model.train()
        for (x_l, y_l), (x_w, x_s) in zip(dl_train, dl_unl):
            x_l = x_l.to(device)
            y_l = y_l.to(device)
            x_w = x_w.to(device)
            x_s = x_s.to(device)

            # Supervised loss
            with torch.cuda.amp.autocast(enabled=amp_enabled):
                logits_l = model(x_l)
                loss_sup = ce(logits_l, y_l)

            # Pseudo labels from weakly augmented unlabeled
            with torch.no_grad():
                logits_w = model(x_w)
                probs_w = torch.softmax(logits_w, dim=1)
                conf, pseudo = probs_w.max(dim=1)
                mask = (conf >= args.tau).float()

            with torch.cuda.amp.autocast(enabled=amp_enabled):
                logits_s = model(x_s)
                loss_unsup = ce(logits_s, pseudo)
                loss_unsup = (loss_unsup * mask).mean()  # only confident ones
                ramp = linear_rampup(epoch, args.rampup_epochs)
                loss = loss_sup + args.lambda_u * ramp * loss_unsup

            opt.zero_grad(set_to_none=True)
            scaler.scale(loss).backward()
            scaler.step(opt)
            scaler.update()
            ema.update(model)

            # Logging
            mask_rate = mask.mean().item()
            logger.add_scalar('train/loss_sup', loss_sup.item(), global_step)
            logger.add_scalar('train/loss_unsup', loss_unsup.item(), global_step)
            logger.add_scalar('train/mask_rate', mask_rate, global_step)
            logger.log_row(epoch, global_step, loss_sup.item(), loss_unsup.item(), mask_rate, float('nan'))

            global_step += 1

        # Validation (EMA model)
        model.eval()
        ema_model = ema.ema
        ema_model.eval()
        ys_all, logits_all = [], []
        with torch.no_grad():
            for x, y in dl_val:
                x = x.to(device)
                y = y.to(device)
                logits = ema_model(x)
                ys_all.append(y)
                logits_all.append(logits)
        ys_all = torch.cat(ys_all, dim=0)
        logits_all = torch.cat(logits_all, dim=0)
        val_auc, val_acc, val_f1 = binary_metrics_from_logits(logits_all, ys_all)
        logger.add_scalar('val/auc', val_auc, epoch)
        logger.add_scalar('val/acc', val_acc, epoch)
        logger.add_scalar('val/f1', val_f1, epoch)

        # Save best
        if val_auc > best_val_auc:
            best_val_auc = val_auc
            torch.save({'model_ema': ema.state_dict(), 'args': vars(args)}, args.out_dir / 'best_ema.pth')

        print(f"Epoch {epoch}: val_auc={val_auc:.4f}, val_acc={val_acc:.4f}, val_f1={val_f1:.4f}, mask_rate~{mask_rate:.3f}")

    logger.close()


if __name__ == '__main__':
    main()

