#!/usr/bin/env python3
"""
Launcher script for FixMatch training.
This script ensures proper Python path setup.
"""

import sys
import subprocess
from pathlib import Path

# Add current directory to Python path
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

# Import and run the training script
if __name__ == "__main__":
    # Pass all command line arguments to the training script
    args = sys.argv[1:]
    
    # Import the main function from training script
    from training.train_fixmatch import main
    
    # Temporarily modify sys.argv to pass arguments
    original_argv = sys.argv
    sys.argv = ['train_fixmatch.py'] + args
    
    try:
        main()
    finally:
        sys.argv = original_argv
