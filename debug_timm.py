import timm
import torch

print("=== Debugging timm model creation ===")

# Test different scenarios
print("\n1. Creating vit_small_patch16_224 with pretrained=False:")
model1 = timm.create_model('vit_small_patch16_224', pretrained=False, num_classes=2)
print(f"   embed_dim: {model1.embed_dim}")
print(f"   num_classes: {model1.head.out_features}")

print("\n2. Creating vit_small_patch16_224 with pretrained=True:")
model2 = timm.create_model('vit_small_patch16_224', pretrained=True, num_classes=2)
print(f"   embed_dim: {model2.embed_dim}")
print(f"   num_classes: {model2.head.out_features}")

print("\n3. Creating vit_small_patch16_224 with pretrained=True, num_classes=1000:")
model3 = timm.create_model('vit_small_patch16_224', pretrained=True, num_classes=1000)
print(f"   embed_dim: {model3.embed_dim}")
print(f"   num_classes: {model3.head.out_features}")

print("\n4. Checking timm version:")
print(f"   timm version: {timm.__version__}")

print("\n5. Checking available models:")
models = timm.list_models('vit_small_patch16*')
print(f"   Available vit_small_patch16 models: {models}")

print("\n6. Model info for vit_small_patch16_224:")
try:
    info = timm.models.get_pretrained_cfg('vit_small_patch16_224')
    print(f"   Pretrained config: {info}")
except Exception as e:
    print(f"   Error getting config: {e}")
